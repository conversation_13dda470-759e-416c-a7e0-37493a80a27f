"use client";

import { NestedCheckboxItem } from "@workspace/ui/components/nested-checkbox";
import { ActivityBrowserDialog } from "@/components/activity-browser.dialog";

// Activity categories data structure
const activityCategories: NestedCheckboxItem[] = [
  {
    id: "oyun",
    label: "<PERSON><PERSON>",
    children: [
      {
        id: "reka<PERSON><PERSON>",
        label: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        children: [
          { id: "fps", label: "FPS" },
          { id: "moba", label: "<PERSON>O<PERSON>" },
          { id: "battle-royale", label: "Battle Royale" },
        ],
      },
      {
        id: "senaryo",
        label: "<PERSON><PERSON><PERSON> Odaklı",
      },
      {
        id: "mmo-rpg",
        label: "MMO",
      },
      {
        id: "masa-parti",
        label: "<PERSON><PERSON>ları",
      },
    ],
  },
  {
    id: "reaksiyon",
    label: "<PERSON>aks<PERSON>yon",
  },
  {
    id: "uretim",
    label: "Üretim",
  },
];

export default function DialogDemo() {
  return <ActivityBrowserDialog activityCategories={activityCategories} />;
}
