import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useConfig, usePaginationConfig } from '../use-config';
import React from 'react';

// Mock the config data
jest.mock('../../public/mocks/config.json', () => ({
  pagination: {
    itemsPerPage: 20,
    maxVisiblePages: 7
  },
  ui: {
    searchDebounceMs: 500,
    loadingDelayMs: 200
  },
  cache: {
    activitiesStaleTimeMs: 600000,
    categoriesStaleTimeMs: 1200000
  }
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    React.createElement(QueryClientProvider, { client: queryClient }, children)
  );
};

describe('useConfig', () => {
  it('should fetch config successfully', async () => {
    const { result } = renderHook(() => useConfig(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toBeDefined();
    expect(result.current.data?.pagination.itemsPerPage).toBe(20);
    expect(result.current.data?.pagination.maxVisiblePages).toBe(7);
    expect(result.current.data?.ui.searchDebounceMs).toBe(500);
  });
});

describe('usePaginationConfig', () => {
  it('should return pagination config with defaults when data is not loaded', () => {
    const { result } = renderHook(() => usePaginationConfig(), {
      wrapper: createWrapper(),
    });

    // Should return defaults immediately
    expect(result.current.itemsPerPage).toBe(15); // default fallback
    expect(result.current.maxVisiblePages).toBe(5); // default fallback
  });

  it('should return pagination config from loaded data', async () => {
    const { result } = renderHook(() => usePaginationConfig(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.itemsPerPage).toBe(20); // from mocked config
    });

    expect(result.current.maxVisiblePages).toBe(7); // from mocked config
  });
});
