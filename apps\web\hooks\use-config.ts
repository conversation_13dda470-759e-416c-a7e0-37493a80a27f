import { useQuery } from "@tanstack/react-query";

export interface AppConfig {
  pagination: {
    itemsPerPage: number;
    maxVisiblePages: number;
  };
  ui: {
    searchDebounceMs: number;
    loadingDelayMs: number;
  };
  cache: {
    activitiesStaleTimeMs: number;
    categoriesStaleTimeMs: number;
  };
}

// Mock function to simulate API call for configuration
const fetchConfig = async (): Promise<AppConfig> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 50));
  
  // Import the config data
  const configData = await import("../public/mocks/config.json");
  return configData.default as AppConfig;
};

export const useConfig = () => {
  return useQuery({
    queryKey: ["config"],
    queryFn: fetchConfig,
    staleTime: 10 * 60 * 1000, // 10 minutes - config doesn't change often
  });
};

// Hook to get specific config values with defaults
export const usePaginationConfig = () => {
  const { data: config } = useConfig();
  
  return {
    itemsPerPage: config?.pagination.itemsPerPage ?? 15,
    maxVisiblePages: config?.pagination.maxVisiblePages ?? 5,
  };
};
