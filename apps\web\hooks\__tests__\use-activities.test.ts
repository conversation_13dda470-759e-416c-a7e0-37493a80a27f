import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useActivities, useActivitiesFiltered } from '../use-activities';
import React from 'react';

// Mock the activities data
jest.mock('../../public/mocks/activities.json', () => [
  {
    title: 'Test Activity 1',
    description: 'Test description 1',
    'senpai-count': 10,
    hot: true,
    fav: false,
    tags: ['test'],
    cover: 'test-cover.jpg',
    icon: 'test-icon.jpg',
  },
  {
    title: 'Test Activity 2',
    description: 'Test description 2',
    'senpai-count': 5,
    hot: false,
    fav: true,
    tags: ['test2'],
    cover: 'test-cover2.jpg',
    icon: 'test-icon2.jpg',
  },
]);

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    React.createElement(QueryClientProvider, { client: queryClient }, children)
  );
};

describe('useActivities', () => {
  it('should fetch activities successfully', async () => {
    const { result } = renderHook(() => useActivities(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toHaveLength(2);
    expect(result.current.data?.[0].title).toBe('Test Activity 1');
  });
});

describe('useActivitiesFiltered', () => {
  it('should filter activities by search value', async () => {
    const { result } = renderHook(
      () => useActivitiesFiltered({ searchValue: 'Test Activity 1' }),
      {
        wrapper: createWrapper(),
      }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toHaveLength(1);
    expect(result.current.data?.[0].title).toBe('Test Activity 1');
  });

  it('should filter activities by favorites', async () => {
    const { result } = renderHook(
      () => useActivitiesFiltered({ showFavorites: true }),
      {
        wrapper: createWrapper(),
      }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toHaveLength(1);
    expect(result.current.data?.[0].title).toBe('Test Activity 2');
  });

  it('should handle pagination correctly', async () => {
    const { result } = renderHook(
      () => useActivitiesFiltered({ page: 1, itemsPerPage: 1 }),
      {
        wrapper: createWrapper(),
      }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toHaveLength(1);
    expect(result.current.totalPages).toBe(2);
    expect(result.current.hasNextPage).toBe(true);
    expect(result.current.hasPreviousPage).toBe(false);
  });
});
